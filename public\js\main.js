// Main Application Logic - Page Routing, Initial Setup, Navigation Event Delegation

// Global Variables
const allContentSections = document.querySelectorAll('section:not(.modal)');
let currentActiveSection = null;
const topNav = document.getElementById('top-nav');
let userName = "Guest";

// Section Visibility and Animation Logic
function showSection(targetId, smoothScroll = true) {
    const targetSection = document.querySelector(targetId);

    if (!targetSection) {
        console.warn(`Target section ${targetId} not found.`);
        return;
    }

    // Protect dashboard page
    if (targetId === '#ai-dashboard-page' && !isLoggedIn) {
        console.log("Access to dashboard denied. Not logged in, redirecting to login page.");
        showSection('#login-page', false);
        return;
    }

     // If the target is the dashboard, and we are already logged in,
     // ensure we activate the default dashboard view and don't re-hide/show the section itself
     if (targetId === '#ai-dashboard-page' && isLoggedIn && currentActiveSection === targetSection) {
         // Determine which dashboard view to activate. New default is 'dashboard-overview-view'.
         const urlParams = new URLSearchParams(window.location.search); // Get query params if used
         const hash = window.location.hash;

         // NEW DEFAULT VIEW: Overview
         const defaultViewId = 'dashboard-overview-view';
         let targetViewId = defaultViewId;

         // Check query parameter first (more explicit for internal views)
         if (urlParams.has('view')) {
             targetViewId = urlParams.get('view');
         } else {
             // Fallback to checking hash if no query param (less reliable for internal views)
             const dashboardViewLinks = document.querySelectorAll('#dashboard-sidebar-nav a[data-view-id]');
             for (const link of dashboardViewLinks) {
                  if (hash.includes(link.dataset.viewId)) {
                      targetViewId = link.dataset.viewId;
                      break;
                  }
             }
         }
         // If no specific view found or the found view is one of the removed ones, targetViewId remains defaultViewId

         activateDashboardView(targetViewId); // Activate the determined view
         return; // Don't proceed with section animation
     }

    // If already on the target section, just scroll to it
    if (targetSection === currentActiveSection) {
        if (smoothScroll) {
            const navHeight = topNav.offsetHeight;
            const targetOffset = targetSection.offsetTop - navHeight;
            smoothScrollTo(targetOffset, 1200);
        } else {
            window.scrollTo(0, targetSection.offsetTop);
        }
        return;
    }

    // Hide current section and show new one
    if (currentActiveSection) {
        currentActiveSection.classList.remove('is-visible');

        // Use a consistent timeout that matches CSS transition duration (500ms + small buffer)
        const transitionDuration = 550; // Slightly longer than CSS transition (500ms)

        setTimeout(() => {
            if (currentActiveSection) {
                currentActiveSection.style.display = 'none';
                currentActiveSection.style.visibility = 'hidden';
            }
            fadeInNewSection();
        }, transitionDuration);
    } else {
        // No current section, just fade in the new one
        fadeInNewSection();
    }

    function fadeInNewSection() {
        // Ensure all non-target sections are hidden first
        allContentSections.forEach(sec => {
            if (sec !== targetSection) {
                sec.classList.remove('is-visible');
                sec.style.display = 'none';
                sec.style.visibility = 'hidden';
            }
        });

        // Use requestAnimationFrame for consistent timing
        requestAnimationFrame(() => {
            targetSection.style.display = 'flex'; // Ensure flex layout for section-padding centering
            targetSection.style.visibility = 'visible';

            // Use another requestAnimationFrame to ensure the display change is processed
            requestAnimationFrame(() => {
                targetSection.classList.add('is-visible');
                currentActiveSection = targetSection;

                // Update mobile menu visibility based on the new current section
                if (typeof window.updateMobileAuthLinks === 'function') {
                    window.updateMobileAuthLinks();
                }
            });
        });

        // Update dashboard welcome message and activate default view if entering dashboard
        if (targetId === '#ai-dashboard-page') {
            updateDashboardWelcomeMessage(); // Now just sets "Welcome Back"
             // When first showing the dashboard section, activate the default view
             // This is also handled by the specific logic block at the start of showSection now
             // but calling it here ensures *a* view is active if showSection is called directly with #ai-dashboard-page
             // without query params/hash fragments indicating a specific view.
             const urlParams = new URLSearchParams(window.location.search);
             const hashView = window.location.hash.split('?view=')[1]; // Check for hash like #ai-dashboard-page?view=...
             // NEW DEFAULT VIEW: Overview
             const initialViewId = urlParams.get('view') || hashView || 'dashboard-overview-view';
             activateDashboardView(initialViewId);
        }

        // Smooth scroll the window to the target section
        if (smoothScroll) {
            // Calculate target scroll position considering fixed nav bar
            const navHeight = topNav.offsetHeight;
            // Use getBoundingClientRect().top for accurate position relative to viewport
            // Add current scroll position to get absolute document position
            const targetOffset = window.scrollY + targetSection.getBoundingClientRect().top - navHeight;
            smoothScrollTo(targetOffset, 1200);
        } else {
             // No smooth scroll, just jump
             const navHeight = topNav.offsetHeight;
             const targetOffset = window.scrollY + targetSection.getBoundingClientRect().top - navHeight;
             window.scrollTo(0, targetOffset);
        }
    }
}

// Navigation Update Functions
function updateNavForLoginState() {
    const authNavLinksContainer = document.getElementById('auth-nav-links');
    const logoLink = document.getElementById('logo-link');

    if (!authNavLinksContainer || !logoLink) {
        console.warn("Navigation elements not found.");
        return;
    }

    // Update logo link based on login state
    logoLink.href = isLoggedIn ? '#ai-dashboard-page' : '#';

    if (isLoggedIn) {
        // Get user's full name from current user data
        let fullName = 'User';
        let userEmail = currentUser?.email || '';

        if (currentUser?.user_metadata?.full_name) {
            fullName = currentUser.user_metadata.full_name;
        } else if (currentUser?.fullName) {
            fullName = currentUser.fullName;
        } else if (userEmail) {
            fullName = userEmail.split('@')[0]; // Use email prefix as fallback
        }

        const userName = fullName;
        // Use first letter of full name if available, otherwise first letter of email
        const userInitial = fullName ? fullName.charAt(0).toUpperCase() : (userEmail?.charAt(0) || 'U').toUpperCase();
        
        authNavLinksContainer.innerHTML = `
            <div class="relative" id="profile-menu-container">
                <button id="userMenuButton" class="flex items-center justify-center w-10 h-10 rounded-full bg-[#00BFFF] text-black font-bold text-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-black focus:ring-[#00BFFF] transition-all duration-200 transform hover:scale-105">
                    ${userInitial}
                </button>
                <!-- Dropdown menu -->
                <div id="profileDropdown" class="hidden absolute right-0 mt-2 w-48 bg-[#0A0A0A] rounded-md shadow-lg py-1 border border-[#1A1A1A] z-50">
                    <div class="px-4 py-2 border-b border-[#1A1A1A]">
                        <p class="text-sm font-medium text-white">${userName}</p>
                        <p class="text-xs text-gray-400 truncate">${userEmail}</p>
                    </div>
                    <a href="#" id="profileLink" class="block px-4 py-2 text-sm text-gray-300 hover:bg-[#1A1A1A] hover:text-white transition-colors">
                        <i class="mr-2 fas fa-user"></i>Profile
                    </a>
                    <a href="#" id="settingsLink" class="block px-4 py-2 text-sm text-gray-300 hover:bg-[#1A1A1A] hover:text-white transition-colors">
                        <i class="mr-2 fas fa-cog"></i>Settings
                    </a>
                    <div class="border-t border-[#1A1A1A] my-1"></div>
                    <a href="#" id="navLogoutBtn" class="block px-4 py-2 text-sm text-red-400 hover:bg-[#1A1A1A] hover:text-red-300 transition-colors">
                        <i class="mr-2 fas fa-sign-out-alt"></i>Logout
                    </a>
                </div>
            </div>`;
        // Set up the profile menu hover behavior and click handlers after updating the HTML
        setTimeout(() => {
            setupProfileMenuHover();

            // Add click handler for dashboard navigation on user menu button
            const userMenuBtn = document.getElementById('userMenuButton');
            if (userMenuBtn) {
                userMenuBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    showSection('#ai-dashboard-page');
                    // Hide dropdown if it's visible
                    const dropdown = document.getElementById('profileDropdown');
                    if (dropdown && !dropdown.classList.contains('hidden')) {
                        dropdown.classList.add('hidden');
                    }
                });
            }

            // Add click handler for profile link in dropdown
            const profileLink = document.getElementById('profileLink');
            if (profileLink) {
                profileLink.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    // Open profile edit modal
                    if (typeof showProfileEditModal === 'function') {
                        showProfileEditModal();
                    }
                    // Hide the dropdown properly
                    const dropdown = document.getElementById('profileDropdown');
                    if (dropdown) {
                        dropdown.classList.remove('show');
                    }
                });
            }

            // Add click handler for settings link
            const settingsLink = document.getElementById('settingsLink');
            if (settingsLink) {
                settingsLink.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    // Navigate to dashboard and show settings view
                    showSection('#ai-dashboard-page');
                    activateDashboardView('dashboard-settings-view');
                    // Hide the dropdown properly
                    const dropdown = document.getElementById('profileDropdown');
                    if (dropdown) {
                        dropdown.classList.remove('show');
                    }
                });
            }

            // Add click handler for logout button with improved error handling
            const logoutBtn = document.getElementById('navLogoutBtn');
            if (logoutBtn) {
                // Remove any existing event listeners to prevent duplicates
                logoutBtn.replaceWith(logoutBtn.cloneNode(true));
                const newLogoutBtn = document.getElementById('navLogoutBtn');

                newLogoutBtn.addEventListener('click', async (e) => {
                    e.preventDefault();
                    e.stopPropagation();

                    console.log('Logout button clicked - performing instant logout'); // Debug log

                    // Prevent logout if already in progress
                    if (newLogoutBtn.disabled) {
                        console.log('Logout already in progress, ignoring click');
                        return;
                    }

                    // Disable button immediately to prevent double-clicks
                    newLogoutBtn.disabled = true;

                    // Hide the dropdown immediately
                    const dropdown = document.getElementById('profileDropdown');
                    if (dropdown) {
                        dropdown.classList.remove('show');
                    }

                    try {
                        // Call the instant logout function
                        await logout();
                    } catch (error) {
                        console.error('Logout failed:', error);
                        // Re-enable button on error
                        newLogoutBtn.disabled = false;
                        showToast('Logout failed. Please try again.', 'error');
                    }
                });
            }
        }, 0);
    } else {
        authNavLinksContainer.innerHTML = `
            <a href="#" class="nav-link text-white hover:text-[#00BFFF]" data-page="login">Login</a>
            <a href="#" class="px-6 py-2 rounded-full btn-primary text-sm font-semibold relative z-10" data-page="register">Register</a>
        `;
    }

    // Update mobile auth links if the function exists
    if (typeof window.updateMobileAuthLinks === 'function') {
        window.updateMobileAuthLinks();
    }
}

// Function to show loading state for profile button
function setProfileButtonLoading(isLoading = true) {
    const userMenuButton = document.getElementById('userMenuButton');
    if (!userMenuButton) {
        console.warn('Profile button not found, cannot set loading state');
        return;
    }

    if (isLoading) {
        userMenuButton.innerHTML = '<i class="fas fa-spinner fa-spin text-sm"></i>';
        userMenuButton.classList.add('opacity-75');
        userMenuButton.setAttribute('data-loading', 'true');
    } else {
        userMenuButton.classList.remove('opacity-75');
        userMenuButton.removeAttribute('data-loading');

        // If refreshNavigationProfile doesn't update the content, provide fallback
        if (!userMenuButton.textContent || userMenuButton.innerHTML.includes('fa-spinner')) {
            const fallbackInitial = (currentUser?.user_metadata?.full_name || currentUser?.email || 'U').charAt(0).toUpperCase();
            userMenuButton.textContent = fallbackInitial;
        }
    }
}

// Function to refresh navigation UI with updated profile data
function refreshNavigationProfile() {
    if (!isLoggedIn) {
        console.log('User not logged in, skipping navigation profile refresh');
        return;
    }

    const userMenuButton = document.getElementById('userMenuButton');
    const profileDropdown = document.getElementById('profileDropdown');

    if (!userMenuButton || !profileDropdown) {
        console.warn('Profile elements not found, rebuilding navigation');
        // If elements don't exist, rebuild the entire navigation
        updateNavForLoginState();
        return;
    }

    // Ensure we have valid user data before updating
    if (!currentUser || !currentUser.id) {
        console.warn('Invalid user data, cannot refresh navigation profile');
        return;
    }

    // Clear any loading state that might be stuck
    userMenuButton.classList.remove('opacity-75');
    userMenuButton.removeAttribute('data-loading');

    // Get updated user information
    let fullName = 'User';
    let userEmail = currentUser?.email || '';

    if (currentUser?.user_metadata?.full_name) {
        fullName = currentUser.user_metadata.full_name;
    } else if (currentUser?.fullName) {
        fullName = currentUser.fullName;
    } else if (userEmail) {
        fullName = userEmail.split('@')[0];
    }

    const userInitial = fullName ? fullName.charAt(0).toUpperCase() : (userEmail?.charAt(0) || 'U').toUpperCase();

    // Update the button initial (remove loading state)
    userMenuButton.textContent = userInitial;
    userMenuButton.classList.remove('opacity-75');

    // Update the dropdown content
    const nameElement = profileDropdown.querySelector('.text-sm.font-medium');
    const emailElement = profileDropdown.querySelector('.text-xs.text-gray-400');

    if (nameElement) nameElement.textContent = fullName;
    if (emailElement) emailElement.textContent = userEmail;
}



// Mobile menu functionality
function setupMobileMenu() {
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const mobileMenu = document.getElementById('mobile-menu');
    const mobileAuthNavLinks = document.getElementById('mobile-auth-nav-links');

    if (!mobileMenuBtn || !mobileMenu) return;

    // Toggle mobile menu with smooth animation
    mobileMenuBtn.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();

        const isOpen = mobileMenu.classList.contains('show');

        if (isOpen) {
            // Close menu with animation
            closeMobileMenu();
        } else {
            // Open menu with animation
            openMobileMenu();
        }
    });

    // Function to open mobile menu with smooth animation
    function openMobileMenu() {
        mobileMenu.classList.remove('hidden');
        // Force reflow to ensure the element is visible before adding show class
        mobileMenu.offsetHeight;

        requestAnimationFrame(() => {
            mobileMenu.classList.add('show');

            // Update button icon to close (X)
            const icon = mobileMenuBtn.querySelector('svg path');
            if (icon) {
                icon.setAttribute('d', 'M6 18L18 6M6 6l12 12');
            }
        });
    }

    // Function to close mobile menu with smooth animation
    function closeMobileMenu() {
        mobileMenu.classList.remove('show');

        // Update button icon to hamburger
        const icon = mobileMenuBtn.querySelector('svg path');
        if (icon) {
            icon.setAttribute('d', 'M4 6h16M4 12h16M4 18h16');
        }

        // Wait for animation to complete before hiding
        setTimeout(() => {
            if (!mobileMenu.classList.contains('show')) {
                mobileMenu.classList.add('hidden');
            }
        }, 400); // Match the CSS transition duration
    }

    // Close mobile menu when clicking outside
    document.addEventListener('click', (e) => {
        if (!mobileMenu.contains(e.target) && !mobileMenuBtn.contains(e.target)) {
            if (mobileMenu.classList.contains('show')) {
                closeMobileMenu();
            }
        }
    });

    // Close mobile menu when navigation link is clicked
    mobileMenu.addEventListener('click', (e) => {
        const target = e.target.closest('a, button');
        if (target) {
            closeMobileMenu();
        }
    });

    // Function to update mobile menu visibility based on current page
    function updateMobileMenuForCurrentPage() {
        const mobileMainNavLinks = document.getElementById('mobile-main-nav-links');
        const mobileAuthNavLinks = document.getElementById('mobile-auth-nav-links');
        const mobileMenuBtn = document.getElementById('mobile-menu-btn');
        if (!mobileMainNavLinks) return;

        // Check if we're currently on the dashboard page using the global currentActiveSection
        const isDashboardPage = currentActiveSection && currentActiveSection.id === 'ai-dashboard-page';

        if (isDashboardPage && isLoggedIn) {
            // Hide main website navigation when in dashboard
            mobileMainNavLinks.style.display = 'none';
            // Remove border-top from auth links since main nav is hidden
            if (mobileAuthNavLinks) {
                mobileAuthNavLinks.classList.remove('pt-3', 'border-t', 'border-gray-800');
            }
            // Hide the mobile menu button entirely on dashboard since it would be empty
            if (mobileMenuBtn) {
                mobileMenuBtn.style.display = 'none';
            }
        } else {
            // Show main website navigation for other pages
            mobileMainNavLinks.style.display = 'block';
            // Add border-top to auth links to separate from main nav (only if auth links exist)
            if (mobileAuthNavLinks && mobileAuthNavLinks.innerHTML.trim() !== '') {
                mobileAuthNavLinks.classList.add('pt-3', 'border-t', 'border-gray-800');
            }
            // Show the mobile menu button on non-dashboard pages
            if (mobileMenuBtn) {
                mobileMenuBtn.style.display = 'block';
            }
        }
    }

    // Update mobile auth links based on login state
    function updateMobileAuthLinks() {
        if (!mobileAuthNavLinks) return;

        if (isLoggedIn) {
            // Get user's full name
            let fullName = 'User';
            let userEmail = currentUser?.email || '';

            if (currentUser?.user_metadata?.full_name) {
                fullName = currentUser.user_metadata.full_name;
            } else if (currentUser?.fullName) {
                fullName = currentUser.fullName;
            } else if (userEmail) {
                fullName = userEmail.split('@')[0];
            }

            mobileAuthNavLinks.innerHTML = `
                <a href="#ai-dashboard-page" class="block nav-link text-white hover:text-[#00BFFF] py-2 text-lg">
                    <i class="fas fa-tachometer-alt mr-2"></i>Dashboard
                </a>
                <button id="mobile-logout-btn" class="block w-full text-left nav-link text-red-400 hover:text-red-300 py-2 text-lg">
                    <i class="fas fa-sign-out-alt mr-2"></i>Logout
                </button>
            `;

            // Add logout handler with improved error handling
            const mobileLogoutBtn = document.getElementById('mobile-logout-btn');
            if (mobileLogoutBtn) {
                // Remove any existing event listeners to prevent duplicates
                mobileLogoutBtn.replaceWith(mobileLogoutBtn.cloneNode(true));
                const newMobileLogoutBtn = document.getElementById('mobile-logout-btn');

                newMobileLogoutBtn.addEventListener('click', async (e) => {
                    e.preventDefault();

                    // Prevent logout if already in progress
                    if (newMobileLogoutBtn.disabled) {
                        console.log('Mobile logout already in progress, ignoring click');
                        return;
                    }

                    // Force clear any profile loading states that might interfere
                    if (window.isProfileLoading) {
                        console.log('Clearing profile loading state before mobile logout');
                        window.isProfileLoading = false;
                        if (window.profileLoadingTimeout) {
                            clearTimeout(window.profileLoadingTimeout);
                            window.profileLoadingTimeout = null;
                        }
                    }

                    try {
                        await logout();
                    } catch (error) {
                        console.error('Mobile logout failed:', error);
                        // Force page refresh as fallback
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    }
                });
            }
        } else {
            // Check if we're on dashboard page to conditionally show login/register
            const isDashboardPage = currentActiveSection && currentActiveSection.id === 'ai-dashboard-page';

            if (isDashboardPage) {
                // Hide login/register buttons when on dashboard (even if not logged in)
                mobileAuthNavLinks.innerHTML = '';
            } else {
                // Show login/register buttons on other pages when not logged in
                mobileAuthNavLinks.innerHTML = `
                    <a href="#" class="block nav-link text-white hover:text-[#00BFFF] py-2 text-lg" data-page="login">Login</a>
                    <a href="#" class="block px-6 py-3 rounded-full btn-primary text-center font-semibold relative z-10" data-page="register">Register</a>
                `;
            }
        }
    }

    // Initial setup
    updateMobileAuthLinks();
    updateMobileMenuForCurrentPage();

    // Return update function for external use
    return function() {
        updateMobileAuthLinks();
        updateMobileMenuForCurrentPage();
    };
}

// Fix mobile interaction issues by ensuring no overlays are blocking
function fixMobileInteractionIssues() {
    console.log('Checking for mobile interaction issues...');

    // Force hide any stuck loading overlays
    const dashboardLoadingOverlay = document.getElementById('dashboard-loading-overlay');
    if (dashboardLoadingOverlay && !dashboardLoadingOverlay.classList.contains('hidden')) {
        console.log('Found stuck dashboard loading overlay, force hiding...');
        if (typeof forceHideDashboardLoading === 'function') {
            forceHideDashboardLoading();
        } else {
            dashboardLoadingOverlay.classList.add('hidden');
            dashboardLoadingOverlay.style.display = 'none';
            dashboardLoadingOverlay.style.pointerEvents = 'none';
        }
    }

    // Check for any other stuck overlays
    const loadingOverlay = document.getElementById('loading-overlay');
    if (loadingOverlay && !loadingOverlay.classList.contains('hidden')) {
        console.log('Found stuck loading overlay, hiding...');
        loadingOverlay.classList.add('hidden');
        loadingOverlay.style.display = 'none';
        loadingOverlay.style.pointerEvents = 'none';
    }

    // Ensure mobile overlay is properly hidden when not needed
    const dashboardMobileOverlay = document.getElementById('dashboard-mobile-overlay');
    if (dashboardMobileOverlay && !dashboardMobileOverlay.classList.contains('hidden')) {
        const dashboardSidebar = document.getElementById('dashboard-sidebar');
        if (dashboardSidebar && dashboardSidebar.classList.contains('-translate-x-full')) {
            console.log('Found stuck mobile overlay, hiding...');
            dashboardMobileOverlay.classList.add('hidden');
        }
    }

    console.log('Mobile interaction check completed');
}

// Dashboard mobile menu functionality with swipe support
function setupDashboardMobileMenu() {
    const dashboardSidebar = document.getElementById('dashboard-sidebar');
    const dashboardMobileOverlay = document.getElementById('dashboard-mobile-overlay');
    const dashboardContent = document.querySelector('#ai-dashboard-page .flex-1');

    if (!dashboardSidebar || !dashboardMobileOverlay) return;

    let isMenuOpen = false;
    let startX = 0;
    let currentX = 0;
    let isDragging = false;
    let startTime = 0;

    // Function to open the menu
    function openMenu() {
        isMenuOpen = true;
        dashboardSidebar.classList.remove('-translate-x-full');
        dashboardSidebar.classList.add('translate-x-0');
        dashboardMobileOverlay.classList.remove('hidden');
        document.body.style.overflow = 'hidden'; // Prevent background scrolling

        // Add class to hide swipe indicator
        const dashboardPage = document.getElementById('ai-dashboard-page');
        if (dashboardPage) {
            dashboardPage.classList.add('menu-open');
        }
    }

    // Function to close the menu
    function closeMenu() {
        isMenuOpen = false;
        dashboardSidebar.classList.remove('translate-x-0');
        dashboardSidebar.classList.add('-translate-x-full');
        dashboardMobileOverlay.classList.add('hidden');
        document.body.style.overflow = ''; // Restore scrolling

        // Remove class to show swipe indicator
        const dashboardPage = document.getElementById('ai-dashboard-page');
        if (dashboardPage) {
            dashboardPage.classList.remove('menu-open');
        }
    }

    // Button is hidden - menu only accessible via swipe gestures
    // (keeping button element for potential future use or debugging)

    // Close menu when clicking overlay
    dashboardMobileOverlay.addEventListener('click', closeMenu);

    // Close menu when clicking a navigation link
    dashboardSidebar.addEventListener('click', (e) => {
        const target = e.target.closest('a[data-view-id]');
        if (target) {
            closeMenu();
        }
    });

    // Handle dashboard mobile logout button
    const dashboardMobileLogoutBtn = document.getElementById('dashboard-mobile-logout-btn');
    if (dashboardMobileLogoutBtn) {
        dashboardMobileLogoutBtn.addEventListener('click', async (e) => {
            e.preventDefault();
            e.stopPropagation();

            // Prevent logout if already in progress
            if (dashboardMobileLogoutBtn.disabled) {
                console.log('Dashboard mobile logout already in progress, ignoring click');
                return;
            }

            // Disable button to prevent multiple clicks
            dashboardMobileLogoutBtn.disabled = true;

            // Show loading state
            const originalText = dashboardMobileLogoutBtn.innerHTML;
            dashboardMobileLogoutBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Logging out...';

            try {
                // Close the mobile menu first
                closeMenu();

                // Call logout function (assuming it exists globally)
                if (typeof logout === 'function') {
                    await logout();
                } else {
                    console.error('Logout function not found');
                    // Fallback: reload page
                    window.location.reload();
                }
            } catch (error) {
                console.error('Dashboard mobile logout failed:', error);
                // Restore button state
                dashboardMobileLogoutBtn.disabled = false;
                dashboardMobileLogoutBtn.innerHTML = originalText;

                // Show error message or fallback
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            }
        });
    }

    // Touch/swipe functionality
    function handleTouchStart(e) {
        if (window.innerWidth >= 768) return; // Only on mobile

        startX = e.touches[0].clientX;
        currentX = startX; // Initialize currentX
        startTime = Date.now();
        isDragging = false;

        // Check if touch started on a clickable element (button, link, etc.)
        const target = e.target.closest('button, a, [role="button"], input, select, textarea');
        const isClickableElement = target !== null;
        const isNavLink = target?.closest('#dashboard-sidebar-nav') !== null;

        // Only start dragging if:
        // 1. Touch is within swipe area from left edge (and not on a clickable element)
        // 2. Menu is open and touch is not on a navigation link, and not on any clickable element
        if ((startX < 80 && !isClickableElement) || (isMenuOpen && !isNavLink && !isClickableElement)) {
            isDragging = true;
            e.preventDefault(); // Prevent default scrolling when dragging
        }
    }

    function handleTouchMove(e) {
        if (!isDragging || window.innerWidth >= 768) return;

        currentX = e.touches[0].clientX;
        const deltaX = currentX - startX;

        // Only prevent default if we're actually dragging
        e.preventDefault();

        if (!isMenuOpen && deltaX > 0) {
            // Opening gesture - only allow rightward swipe from left edge
            const progress = Math.min(deltaX / 256, 1); // 256px is sidebar width
            const translateX = -100 + (progress * 100);
            dashboardSidebar.style.transform = `translateX(${translateX}%)`;
            dashboardSidebar.style.transition = 'none'; // Disable transition during drag

            if (progress > 0.1) {
                dashboardMobileOverlay.classList.remove('hidden');
                dashboardMobileOverlay.style.opacity = progress * 0.5;
            }
        } else if (isMenuOpen && deltaX < 0) {
            // Closing gesture - only allow leftward swipe when menu is open
            const progress = Math.min(Math.abs(deltaX) / 256, 1);
            const translateX = -(progress * 100);
            dashboardSidebar.style.transform = `translateX(${translateX}%)`;
            dashboardSidebar.style.transition = 'none'; // Disable transition during drag
            dashboardMobileOverlay.style.opacity = (1 - progress) * 0.5;
        }
    }

    function handleTouchEnd(e) {
        if (!isDragging || window.innerWidth >= 768) return;

        const deltaX = currentX - startX;
        const deltaTime = Date.now() - startTime;
        const velocity = Math.abs(deltaX) / deltaTime;

        // Restore transition for smooth animation
        dashboardSidebar.style.transition = '';

        // Reset inline styles
        dashboardSidebar.style.transform = '';
        dashboardMobileOverlay.style.opacity = '';

        // Determine if we should open or close based on distance and velocity
        if (!isMenuOpen) {
            // Opening gesture
            if (deltaX > 128 || (deltaX > 50 && velocity > 0.3)) {
                openMenu();
            } else {
                closeMenu();
            }
        } else {
            // Closing gesture
            if (deltaX < -128 || (deltaX < -50 && velocity > 0.3)) {
                closeMenu();
            } else {
                openMenu();
            }
        }

        isDragging = false;
    }

    // Add touch event listeners to the dashboard content area for swipe gestures
    if (dashboardContent) {
        dashboardContent.addEventListener('touchstart', handleTouchStart, { passive: false });
        dashboardContent.addEventListener('touchmove', handleTouchMove, { passive: false });
        dashboardContent.addEventListener('touchend', handleTouchEnd, { passive: true });
    }

    // Also add to the sidebar for closing gestures, but use passive listeners for better performance
    // when not actively dragging
    dashboardSidebar.addEventListener('touchstart', handleTouchStart, { passive: false });
    dashboardSidebar.addEventListener('touchmove', handleTouchMove, { passive: false });
    dashboardSidebar.addEventListener('touchend', handleTouchEnd, { passive: true });

    // Ensure navigation links in sidebar are properly clickable on mobile
    const navLinks = dashboardSidebar.querySelectorAll('.nav-link, button');
    navLinks.forEach(link => {
        // Add touch-action CSS property for better touch handling
        link.style.touchAction = 'manipulation';

        // Ensure proper cursor and user-select for mobile
        link.style.cursor = 'pointer';
        link.style.userSelect = 'none';
        link.style.webkitUserSelect = 'none';

        // Add debug logging for touch events
        link.addEventListener('touchstart', (e) => {
            console.log('Touch start on nav link:', link.textContent.trim());
        }, { passive: true });

        link.addEventListener('click', (e) => {
            console.log('Click event on nav link:', link.textContent.trim());
        }, { passive: true });
    });

    // Add global debug for touch issues
    if (window.innerWidth < 768) {
        console.log('Mobile dashboard navigation setup complete');
        console.log('Dashboard sidebar:', dashboardSidebar ? 'found' : 'not found');
        console.log('Navigation links count:', navLinks.length);
    }

    // Close menu on window resize to desktop
    window.addEventListener('resize', () => {
        if (window.innerWidth >= 768 && isMenuOpen) {
            closeMenu();
        }
        // Fix mobile interaction issues on resize
        if (window.innerWidth < 768) {
            setTimeout(fixMobileInteractionIssues, 100);
        }
    });
}

// Mobile keyboard handling for login/register forms
function handleMobileKeyboard(sectionId, inputElement) {
    if (window.innerWidth > 768) return; // Only for mobile devices

    // Use visual viewport API if available, fallback to window.innerHeight
    const getViewportHeight = () => {
        return window.visualViewport ? window.visualViewport.height : window.innerHeight;
    };

    let originalViewportHeight = getViewportHeight();
    let keyboardVisible = false;

    // Function to scroll form above keyboard
    function scrollFormAboveKeyboard() {
        const section = document.querySelector(sectionId);
        const formContainer = section?.querySelector('.max-w-md');

        if (!section || !formContainer) return;

        // Calculate the position to scroll to
        const navHeight = document.getElementById('top-nav')?.offsetHeight || 80;
        const currentViewportHeight = getViewportHeight();
        const keyboardHeight = Math.max(250, originalViewportHeight - currentViewportHeight); // Estimate keyboard height
        const availableHeight = currentViewportHeight - navHeight;

        // Get form container position
        const formRect = formContainer.getBoundingClientRect();
        const formHeight = formContainer.offsetHeight;

        // Calculate target scroll position to center form in available space
        const targetScrollY = window.scrollY + formRect.top - navHeight - Math.max(0, (availableHeight - formHeight) / 2);

        // Smooth scroll to position
        window.scrollTo({
            top: Math.max(0, targetScrollY),
            behavior: 'smooth'
        });
    }

    // Function to detect keyboard visibility changes
    function detectKeyboardVisibility() {
        const currentHeight = getViewportHeight();
        const heightDifference = originalViewportHeight - currentHeight;

        // Keyboard is considered visible if viewport height decreased by more than 150px
        const isKeyboardNowVisible = heightDifference > 150;

        if (isKeyboardNowVisible && !keyboardVisible) {
            // Keyboard just appeared
            keyboardVisible = true;
            document.body.classList.add('mobile-keyboard-active');
            setTimeout(scrollFormAboveKeyboard, 300); // Small delay for keyboard animation
        } else if (!isKeyboardNowVisible && keyboardVisible) {
            // Keyboard just disappeared
            keyboardVisible = false;
            document.body.classList.remove('mobile-keyboard-active');
        }
    }

    // Listen for viewport changes (keyboard show/hide)
    const resizeHandler = () => {
        detectKeyboardVisibility();
    };

    // Listen for visual viewport changes (better keyboard detection)
    const visualViewportHandler = () => {
        detectKeyboardVisibility();
    };

    // Listen for focus events on form inputs
    const focusHandler = (event) => {
        if (event.target.matches('input, textarea')) {
            setTimeout(() => {
                detectKeyboardVisibility();
            }, 300); // Delay to allow keyboard to appear
        }
    };

    // Add event listeners
    window.addEventListener('resize', resizeHandler);
    if (window.visualViewport) {
        window.visualViewport.addEventListener('resize', visualViewportHandler);
    }
    document.addEventListener('focusin', focusHandler);

    // Initial check after a short delay
    setTimeout(detectKeyboardVisibility, 500);

    // Cleanup function to remove listeners when section changes
    const cleanup = () => {
        window.removeEventListener('resize', resizeHandler);
        if (window.visualViewport) {
            window.visualViewport.removeEventListener('resize', visualViewportHandler);
        }
        document.removeEventListener('focusin', focusHandler);
    };

    // Store cleanup function for later use
    if (!window.mobileKeyboardCleanups) {
        window.mobileKeyboardCleanups = [];
    }
    window.mobileKeyboardCleanups.push(cleanup);

    // Clean up previous handlers to avoid memory leaks
    if (window.mobileKeyboardCleanups.length > 3) {
        const oldCleanup = window.mobileKeyboardCleanups.shift();
        oldCleanup();
    }
}

// Setup navigation event listeners
function setupNavigationEventListeners() {
    // Setup mobile menu
    const updateMobileAuthLinks = setupMobileMenu();

    // Store the update function globally for use in auth state changes
    window.updateMobileAuthLinks = updateMobileAuthLinks;

    // Setup dashboard mobile menu with swipe support
    setupDashboardMobileMenu();

    // Fix any mobile interaction issues on load
    if (window.innerWidth < 768) {
        setTimeout(fixMobileInteractionIssues, 500);

        // Add emergency touch handler for stuck overlays
        let touchStartTime = 0;
        document.addEventListener('touchstart', (e) => {
            touchStartTime = Date.now();
        }, { passive: true });

        document.addEventListener('touchend', (e) => {
            const touchDuration = Date.now() - touchStartTime;
            // If touch was longer than 2 seconds, check for stuck overlays
            if (touchDuration > 2000) {
                console.log('Long touch detected, checking for stuck overlays...');
                fixMobileInteractionIssues();
            }
        }, { passive: true });
    }

    // Main navigation links
    document.addEventListener('click', (event) => {
        const target = event.target.closest('a[href^="#"]');
        if (!target) return;

        event.preventDefault();
        const href = target.getAttribute('href');

        if (href && href !== '#') {
            showSection(href);
        }
    });

    // Data-page navigation (for login/register buttons)
    document.addEventListener('click', (event) => {
        const target = event.target.closest('[data-page]');
        if (!target) return;

        event.preventDefault();
        const page = target.getAttribute('data-page');

        if (page === 'login') {
            showSection('#login-page');
            // Focus on the first input field after the section transition and handle mobile keyboard
            setTimeout(() => {
                const firstInput = document.querySelector('#login-page input[type="email"]');
                if (firstInput) {
                    firstInput.focus();
                    // Handle mobile keyboard positioning
                    if (window.innerWidth <= 768) {
                        handleMobileKeyboard('#login-page', firstInput);
                    }
                }
            }, 600); // Wait for section transition to complete (550ms + buffer)
        } else if (page === 'register') {
            showSection('#register-page');
            // Focus on the first input field after the section transition and handle mobile keyboard
            setTimeout(() => {
                const firstInput = document.querySelector('#register-page input[type="text"]');
                if (firstInput) {
                    firstInput.focus();
                    // Handle mobile keyboard positioning
                    if (window.innerWidth <= 768) {
                        handleMobileKeyboard('#register-page', firstInput);
                    }
                }
            }, 600); // Wait for section transition to complete (550ms + buffer)
        }
    });

    // Dashboard sidebar navigation
    document.addEventListener('click', (event) => {
        const target = event.target.closest('[data-view-id]');
        if (!target) return;

        event.preventDefault();
        const viewId = target.getAttribute('data-view-id');

        if (viewId) {
            activateDashboardView(viewId);
        }
    });
}

// Setup form event listeners
function setupFormEventListeners() {
    // Login form
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);

        // Add mobile keyboard handling to login form inputs
        if (window.innerWidth <= 768) {
            const loginInputs = loginForm.querySelectorAll('input');
            loginInputs.forEach(input => {
                input.addEventListener('focus', () => {
                    setTimeout(() => {
                        handleMobileKeyboard('#login-page', input);
                    }, 100);
                });
            });
        }
    }

    // Register form
    const registerForm = document.getElementById('registerForm');
    if (registerForm) {
        registerForm.addEventListener('submit', handleRegister);

        // Add mobile keyboard handling to register form inputs
        if (window.innerWidth <= 768) {
            const registerInputs = registerForm.querySelectorAll('input');
            registerInputs.forEach(input => {
                input.addEventListener('focus', () => {
                    setTimeout(() => {
                        handleMobileKeyboard('#register-page', input);
                    }, 100);
                });
            });
        }
    }
    
    // Contact form
    const contactForm = document.getElementById('contactForm');
    if (contactForm) {
        contactForm.addEventListener('submit', handleContactForm);
    }
    
    // AI Agent form
    const aiAgentForm = document.getElementById('ai-agent-form');
    if (aiAgentForm) {
        aiAgentForm.addEventListener('submit', handleAIAgentForm);
    }

    // Company creation form
    const companyCreationForm = document.getElementById('companyCreationForm');
    if (companyCreationForm) {
        companyCreationForm.addEventListener('submit', handleCompanyCreation);
    }
    
    // Settings form (if it exists)
    const settingsForm = document.getElementById('settingsForm');
    if (settingsForm) {
        settingsForm.addEventListener('submit', handleSettingsUpdate);
    }

    // Dashboard support form (different from main support form)
    const dashboardSupportForm = document.getElementById('supportForm');
    if (dashboardSupportForm) {
        dashboardSupportForm.addEventListener('submit', handleDashboardSupportFormSubmission);
    }



    // Company management refresh button
    const refreshCompaniesBtn = document.getElementById('refresh-companies-btn');
    if (refreshCompaniesBtn) {
        refreshCompaniesBtn.addEventListener('click', async () => {
            console.log('Refresh companies button clicked');
            if (typeof populateCompaniesManagement === 'function') {
                await populateCompaniesManagement();
            }
        });
    }

    // Client management refresh button
    const refreshClientsBtn = document.getElementById('refresh-clients-btn');
    if (refreshClientsBtn) {
        refreshClientsBtn.addEventListener('click', async () => {
            console.log('Refresh clients button clicked');
            if (typeof populateClientsManagement === 'function') {
                await populateClientsManagement();
            }
        });
    }

    // Edit Profile button
    const editProfileBtn = document.getElementById('edit-profile-btn');
    if (editProfileBtn) {
        editProfileBtn.addEventListener('click', (e) => {
            e.preventDefault();
            if (typeof showProfileEditModal === 'function') {
                showProfileEditModal();
            }
        });
    }

    // Refresh AI Agents button handler
    const refreshAgentsBtn = document.getElementById('refresh-agents-btn');
    if (refreshAgentsBtn) {
        refreshAgentsBtn.addEventListener('click', async () => {
            console.log('Refresh agents button clicked');
            if (typeof populateAIAgentsList === 'function') {
                await populateAIAgentsList();
            } else {
                console.error('populateAIAgentsList function not available');
            }
        });
    }

    // FAQ accordion functionality
    setupFAQAccordion();
}

// FAQ Accordion Setup
function setupFAQAccordion() {
    const faqQuestions = document.querySelectorAll('.faq-question');

    faqQuestions.forEach(question => {
        question.addEventListener('click', () => {
            const faqItem = question.closest('.faq-item');
            const answer = faqItem.querySelector('.faq-answer');
            const icon = question.querySelector('i');
            const isOpen = !answer.classList.contains('hidden');

            // Close all other FAQ items first
            faqQuestions.forEach(otherQuestion => {
                if (otherQuestion !== question) {
                    const otherFaqItem = otherQuestion.closest('.faq-item');
                    const otherAnswer = otherFaqItem.querySelector('.faq-answer');
                    const otherIcon = otherQuestion.querySelector('i');

                    if (!otherAnswer.classList.contains('hidden')) {
                        closeAccordionItem(otherAnswer, otherIcon);
                    }
                }
            });

            // Toggle current item
            if (isOpen) {
                closeAccordionItem(answer, icon);
            } else {
                openAccordionItem(answer, icon);
            }
        });
    });
}

// Helper function to open accordion item smoothly
function openAccordionItem(answer, icon) {
    // Remove hidden class and prepare for animation
    answer.classList.remove('hidden');
    answer.classList.add('expanding');

    // Set initial state without transition
    answer.style.transition = 'none';
    answer.style.maxHeight = '0px';
    answer.style.opacity = '0';

    // Force reflow to ensure initial state is applied
    answer.offsetHeight;

    // Re-enable transitions
    answer.style.transition = '';

    // Use requestAnimationFrame to ensure smooth animation start
    requestAnimationFrame(() => {
        // Get the natural height by temporarily setting auto
        answer.style.maxHeight = 'auto';
        const scrollHeight = answer.scrollHeight;
        answer.style.maxHeight = '0px';

        // Force reflow again
        answer.offsetHeight;

        // Start the animation
        requestAnimationFrame(() => {
            answer.style.maxHeight = scrollHeight + 'px';
            answer.style.opacity = '1';
            icon.style.transform = 'rotate(180deg)';

            // Clean up after animation
            setTimeout(() => {
                if (answer.classList.contains('expanding')) {
                    answer.style.maxHeight = 'auto';
                }
            }, 400);
        });
    });
}

// Helper function to close accordion item smoothly
function closeAccordionItem(answer, icon) {
    // Set current height explicitly before starting close animation
    answer.style.maxHeight = answer.scrollHeight + 'px';
    answer.classList.remove('expanding');

    // Force reflow
    answer.offsetHeight;

    // Start close animation
    requestAnimationFrame(() => {
        answer.style.maxHeight = '0px';
        answer.style.opacity = '0';
        icon.style.transform = 'rotate(0deg)';

        setTimeout(() => {
            answer.classList.add('hidden');
            answer.style.maxHeight = '';
            answer.style.opacity = '';
        }, 400);
    });
}

// FAQ Modal Functions
const faqData = {
    faq1: {
        title: "What is Veritas Agent and how does it work?",
        content: "Veritas Agent is a comprehensive AI automation platform that creates custom AI agents tailored to your business needs. Our system integrates seamlessly with your existing software ecosystem, analyzes your workflows, and develops bespoke automation solutions that deliver measurable ROI. We handle everything from discovery and integration to development and optimization."
    },
    faq2: {
        title: "How long does it take to implement AI automation?",
        content: "Implementation timelines vary based on complexity, but most projects follow our three-phase approach: Discovery & Integration (1-2 weeks), Bespoke AI Agent Development (2-4 weeks), and High ROI Automation & Optimization (1-2 weeks). Simple integrations can be completed in as little as 2-3 weeks, while complex enterprise solutions may take 6-8 weeks."
    },
    faq3: {
        title: "What types of software and platforms do you integrate with?",
        content: "We integrate with a wide range of platforms including email systems (Gmail, Outlook), communication tools (Slack, Telegram), productivity suites (Google Workspace, Microsoft 365), databases (Airtable, Notion), CRM systems, accounting software, and custom APIs. Our platform supports both cloud-based and on-premise solutions."
    },
    faq4: {
        title: "How much does Veritas Agent cost?",
        content: "Our pricing is customized based on your specific needs, complexity, and scale. We offer flexible packages starting from small business solutions to enterprise-level implementations. Contact us for a free consultation where we'll assess your requirements and provide a detailed quote. We focus on delivering high ROI solutions that pay for themselves through efficiency gains."
    },
    faq5: {
        title: "Is my data secure with Veritas Agent?",
        content: "Absolutely. We implement enterprise-grade security measures including end-to-end encryption, secure API connections, role-based access controls, and compliance with industry standards. Your data remains within your control, and we follow strict privacy protocols. All integrations use secure OAuth2 authentication where possible."
    },
    faq6: {
        title: "Do you provide ongoing support and maintenance?",
        content: "Yes! We provide comprehensive ongoing support including monitoring, maintenance, updates, and optimization. Our team continuously refines your AI agents to improve performance and adapt to changing business needs. We also offer training for your team and 24/7 technical support for critical systems."
    }
};

function openFAQModal(faqId) {
    const modal = document.getElementById('faq-modal');
    const title = document.getElementById('faq-modal-title');
    const content = document.getElementById('faq-modal-content');

    if (faqData[faqId]) {
        title.textContent = faqData[faqId].title;
        content.textContent = faqData[faqId].content;
        modal.classList.remove('hidden');
        document.body.style.overflow = 'hidden'; // Prevent background scrolling
    }
}

function closeFAQModal() {
    const modal = document.getElementById('faq-modal');
    modal.classList.add('hidden');
    document.body.style.overflow = ''; // Restore scrolling
}

// Close modal when clicking outside
document.addEventListener('click', (e) => {
    const modal = document.getElementById('faq-modal');
    if (e.target === modal) {
        closeFAQModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
        closeFAQModal();
    }
});

// Initialize application
async function initializeApp() {
    console.log('Initializing Veritas Agent application...');

    try {
        // Set flag to prevent auth state listener from interfering during initialization
        window.isAppInitializing = true;

        // Set initial loading state
        document.body.classList.add('loading');

        // Add visibility change handler to maintain auth state when switching tabs
        // Simplified to only handle logout button state without triggering profile refreshes
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden && isLoggedIn) {
                // Tab became visible and user is logged in - only fix logout button if needed
                const logoutBtn = document.getElementById('navLogoutBtn');
                if (logoutBtn && logoutBtn.disabled) {
                    console.log('Re-enabling logout button after tab switch');
                    logoutBtn.disabled = false;
                    // Restore original button text if it was stuck in loading state
                    if (logoutBtn.innerHTML.includes('fa-spinner')) {
                        logoutBtn.innerHTML = '<i class="mr-2 fas fa-sign-out-alt"></i>Logout';
                    }
                }
            }
        });

        // Initialize Supabase client
        const supabase = getSupabaseClient();

        if (supabase) {
            // Check for existing session first, before setting up listener
            const { data: { session }, error } = await supabase.auth.getSession();

            if (session) {
                // Clear any existing data from previous sessions
                console.log('Existing session found, clearing previous session data...');
                clearDashboardData();

                // Show dashboard loading for existing sessions
                showDashboardLoading();
                setDashboardLoadingStep('auth', 'completed');
                updateDashboardLoadingProgress(25, 'Welcome Back', 'Restoring your session...');

                isLoggedIn = true;
                currentUser = {
                    id: session.user.id,
                    email: session.user.email,
                    fullName: session.user.user_metadata?.full_name || session.user.email,
                    company: session.user.user_metadata?.company || '',
                    role: 'Client',
                    user_metadata: session.user.user_metadata || {}
                };

                // Update navigation first with basic info
                updateNavForLoginState();

                // Step 2: Loading profile
                setDashboardLoadingStep('profile', 'active');
                updateDashboardLoadingProgress(50, 'Loading Profile', 'Retrieving your account information...');

                // Load user profile and refresh navigation with complete data (only if not already loaded)
                if (!window.cachedUserProfile) {
                    await loadUserProfile(true);
                }

                // Step 3: Setting up permissions
                completeDashboardLoadingStep('profile');
                setDashboardLoadingStep('permissions', 'active');
                updateDashboardLoadingProgress(75, 'Setting Up Permissions', 'Configuring your access level...');

                // Brief delay to show permissions step
                await new Promise(resolve => setTimeout(resolve, 300));

                // Step 4: Preparing dashboard
                completeDashboardLoadingStep('permissions');
                setDashboardLoadingStep('dashboard', 'active');
                updateDashboardLoadingProgress(90, 'Preparing Dashboard', 'Almost ready...');

                // Update navigation for logged-in state
                updateNavForLoginState();
            } else {
                isLoggedIn = false;
                currentUser = null;
                // Update navigation based on auth state
                updateNavForLoginState();
            }

            // Setup auth state listener after initial session check to prevent race conditions
            setupAuthStateListener(supabase);
        }
        
        // Setup event listeners
        setupNavigationEventListeners();
        setupFormEventListeners();
        
        // Initialize modals
        initializeModals();
        
        // Show initial section based on auth state and URL
        const hash = window.location.hash;
        if (isLoggedIn) {
            // SECURITY FIX: For existing sessions, we must load the user profile first
            // to determine if they need to complete company creation before showing dashboard
            console.log('Authenticated user detected, loading profile to determine proper page...');

            // Show loading state while we determine where to redirect
            showDashboardLoading();
            setDashboardLoadingStep('auth', 'completed');
            updateDashboardLoadingProgress(25, 'Verifying Session', 'Checking your account status...');

            // Load user profile first to check company status and role
            await loadUserProfile(true);

            // After profile loads, currentUser.company will be set correctly
            if (currentUser && currentUser.company && currentUser.company !== 'Pending' && currentUser.company !== null) {
                // User has completed company creation, safe to show dashboard
                console.log('User has completed setup, showing dashboard');

                // Start background initialization
                const initializationPromise = typeof initializeDashboardInBackground === 'function'
                    ? initializeDashboardInBackground()
                    : Promise.resolve();

                // Show dashboard
                showSection(hash === '#ai-dashboard-page' ? hash : '#ai-dashboard-page', false);

                // Wait for background initialization to complete
                await initializationPromise;

                // Hide dashboard loading overlay
                hideDashboardLoading();
            } else {
                // User hasn't completed company creation, redirect to company creation page
                console.log('User needs to complete company creation, redirecting...');
                hideDashboardLoading();
                showSection('#company-creation-page');
                showToast('Please complete your company setup to continue.', 'info');
            }
        } else {
            // If not logged in, show login page or requested section (but not dashboard)
            showSection(hash && hash !== '#ai-dashboard-page' ? hash : '#home', false);
        }
        
        // Mark as loaded and show the page
        document.body.classList.remove('loading');
        document.body.classList.add('loaded');

        // Clear initialization flag to allow auth state listener to work normally
        window.isAppInitializing = false;

        // Set up periodic auth state consistency checks (every 30 seconds)
        if (typeof ensureAuthStateConsistency === 'function') {
            setInterval(ensureAuthStateConsistency, 30000);
        }

        console.log('Application initialized successfully');

    } catch (error) {
        console.error('Error initializing application:', error);

        // Clear initialization flag even on error
        window.isAppInitializing = false;

        await hideLoadingOverlay();
        showToast('Error initializing application. Please refresh the page.', 'error');
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    initializeApp();
});
