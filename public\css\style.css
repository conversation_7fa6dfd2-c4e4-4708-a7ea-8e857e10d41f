/* Chosen Palette: "Cyber Noir Enhanced" - A sleek, modern palette using dark charcoal (#111111) for backgrounds, extremely dark grey (#0A0A0A) for primary cards, a slightly lighter dark grey (#1A1A1A) for borders and subtle internal elements, and vibrant neon blue (#00BFFF) as a subtle accent and for interactive highlights. */

/* Button loading state styles remain for form submissions */
.btn-spinner {
    display: none;
    width: 1.25rem;
    height: 1.25rem;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 0.8s ease-in-out infinite;
    margin-right: 0.5rem;
    vertical-align: middle;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.btn-loading .btn-spinner {
    display: inline-block !important;
}

.btn-loading .btn-text {
    vertical-align: middle;
}

:root {
    --PI: 3.***********; /* Define PI for CSS calc() functions */
    --nav-height: 80px; /* More accurate height accounting for padding and content */
    --dashboard-sidebar-width: 256px; /* Width for the dashboard's internal sidebar */
}

/* Prevent flash of unstyled content */
body.loading {
    visibility: hidden;
    opacity: 0;
    transition: opacity 0.3s ease-in, visibility 0.3s;
}

body.loaded {
    visibility: visible;
    opacity: 1;
}

body {
    font-family: 'Inter', sans-serif;
    background-color: #111111; /* Dark Charcoal */
    color: #E0E0E0; /* Light Grey for text */
    margin: 0;
    height: 100vh; /* Ensure body takes full viewport height for full-page sections */
    overflow: hidden; /* Hide body overflow, animated-sections will handle their own */
    transition: opacity 0.3s ease-in;
    scroll-padding-top: var(--nav-height); /* Ensure scroll anchors respect nav bar */
}

.selection\:bg-\[\#00BFFF\]::selection {
    background-color: #00BFFF;
    color: black;
}

/* Ensure navigation bar has consistent height */
#top-nav {
    height: var(--nav-height);
    min-height: var(--nav-height);
    position: fixed;
    top: 0;
    z-index: 50;
}

/* Utility class for elements that need to start below the nav */
.below-nav {
    margin-top: var(--nav-height);
}

/* Ensure all scroll targets respect the navigation bar */
html {
    scroll-padding-top: var(--nav-height);
}

/* Ensure sections with IDs (scroll targets) have proper offset */
section[id] {
    scroll-margin-top: var(--nav-height);
}

/* Adjusted section padding for fixed nav bar and full height sections */
.section-padding {
    padding-top: calc(var(--nav-height) + 2rem); /* Ensure content starts below nav with spacing */
    padding-bottom: 4rem;
    min-height: 100vh; /* Ensure section fills viewport height */
    display: flex; /* Use flex to center content vertically */
    align-items: center; /* Center content vertically */
    justify-content: center; /* Center content horizontally */
    flex-direction: column; /* Stack content vertically */
    box-sizing: border-box; /* Include padding in total height */
}

/* Contact section specific padding override */
#contact.section-padding {
    padding-top: calc(var(--nav-height) + 1rem); /* Tighter spacing for contact */
}

/* Register page specific padding override */
#register-page.section-padding {
    padding-top: calc(var(--nav-height) + 1rem); /* Tighter spacing for register */
}

/* Mobile-first responsive improvements */
@media (max-width: 768px) {
    /* Enable scrolling on mobile devices */
    body {
        overflow: auto !important; /* Override hidden overflow for mobile */
        -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
    }

    /* Adjust animated sections for mobile scrolling */
    .animated-section {
        position: relative !important; /* Use relative positioning on mobile */
        top: 0 !important; /* Reset top positioning */
        height: auto !important; /* Allow natural height */
        overflow: visible !important; /* Allow content to flow naturally */
        -webkit-overflow-scrolling: touch; /* Smooth touch scrolling */
    }

    .section-padding {
        padding-top: calc(var(--nav-height) + 1.5rem); /* Ensure content starts below nav on mobile */
        padding-bottom: 2rem;
    }

    /* Improve touch targets */
    .btn-primary {
        min-height: 48px;
        padding: 12px 24px;
    }

    /* Better mobile navigation */
    #mobile-menu {
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
    }

    /* Smooth hamburger icon animation */
    #mobile-menu-btn svg {
        transition: transform 0.3s ease-in-out;
    }

    #mobile-menu-btn svg path {
        transition: d 0.3s ease-in-out;
    }

    #mobile-menu-btn:hover svg {
        transform: scale(1.1);
    }

    /* Smooth transition for mobile main nav links visibility */
    #mobile-main-nav-links {
        transition: opacity 0.3s ease-in-out, max-height 0.3s ease-in-out;
        overflow: hidden;
    }

    #mobile-main-nav-links[style*="display: none"] {
        opacity: 0;
        max-height: 0;
    }

    /* Improve form inputs for mobile */
    input, textarea, button {
        font-size: 16px; /* Prevent zoom on iOS */
    }

    /* Better mobile search */
    #company-search-input {
        padding: 0.875rem 2.5rem 0.875rem 2.5rem;
        font-size: 16px;
    }

    /* Mobile-friendly refresh button */
    #refresh-companies-btn {
        min-height: 44px;
        padding: 0.75rem 1rem;
        font-size: 16px;
    }

    /* Clear search button mobile improvements */
    #clear-company-search-btn {
        padding: 0.5rem;
        min-height: 32px;
        min-width: 32px;
    }

    /* Search icon positioning fix */
    .relative .absolute {
        z-index: 10;
    }

    /* Ensure touch events work properly */
    * {
        touch-action: manipulation;
    }

    /* Prevent bounce scrolling on iOS */
    html, body {
        overscroll-behavior: none;
    }
}

.neon-blue-glow {
    box-shadow: 0 0 10px #00BFFF, 0 0 20px #00BFFF, 0 0 30px #00BFFF;
}

.btn-primary {
    background-color: #00BFFF;
    color: #000000;
    transition: all 0.3s ease-in-out;
    position: relative;
    overflow: hidden;
}

.btn-primary:hover {
    background-color: #00FFFF;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 191, 255, 0.4);
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 300%;
    height: 300%;
    background-color: rgba(255, 255, 255, 0.15);
    border-radius: 50%;
    transition: all 0.5s ease-in-out;
    transform: translate(-50%, -50%) scale(0);
    z-index: 0;
}

.btn-primary:hover::before {
    transform: translate(-50%, -50%) scale(1);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 191, 255, 0.2);
}

.nav-link {
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: #00BFFF;
}

/* Section Entry Animations - Full Page Overlay */
.animated-section {
    opacity: 0;
    transform: translateY(30px) scale(0.98);
    transition: opacity 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94), transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    pointer-events: none; /* Disable interaction when not visible */
    position: absolute; /* Full page overlay */
    top: var(--nav-height); /* Start below the fixed navigation bar */
    left: 0;
    width: 100%;
    height: calc(100vh - var(--nav-height)); /* Adjust height to account for nav bar */
    overflow-y: auto; /* Allow scrolling within the section if content overflows */
    box-sizing: border-box; /* Include padding in total height */
    visibility: hidden; /* Hide completely when not visible */
    will-change: opacity, transform; /* Optimize for animations */
}

.animated-section.is-visible {
    opacity: 1;
    transform: translateY(0) scale(1); /* Return to normal scale */
    pointer-events: all; /* Enable interaction when visible */
    position: relative; /* Revert to relative to occupy space */
    visibility: visible; /* Make visible */
    top: 0; /* Reset top when in relative position */
    height: auto; /* Allow natural height when in relative position */
}

/* Home Section Background Animation */
.home-bg-animation {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, transparent 0%, #00BFFF10 20%, #00BFFF33 50%, #00BFFF10 80%, transparent 100%);
    background-size: 250% 250%;
    animation: wavyFlow 20s ease-in-out infinite alternate;
    opacity: 0.4;
    z-index: 1;
}

@keyframes wavyFlow {
    0% { background-position: 0% 0%; }
    100% { background-position: 100% 100%; }
}

/* Particle Overlay - MODIFIED FOR BRIGHTER, SOFTER DOTS */
.particle-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    /* Modified gradient for softer, more prominent particles */
    background-image: radial-gradient(circle at center, #00BFFF 0%, #00BFFF 1px, transparent 3px);
    background-size: 30px 30px; /* Keep spacing */
    opacity: 0.25; /* Increased opacity significantly from 0.06 */
    animation: particleDrift 30s linear infinite;
    z-index: 5; /* Keep it above the wavy background, but below content */
}

@keyframes particleDrift {
    0% { background-position: 0 0; }
    100% { background-position: 300px 300px; }
}

/* Modal styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0,0,0,0.8);
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.modal-visible {
    opacity: 1;
}

.modal-content {
    background-color: #0A0A0A;
    margin: 20px auto;
    padding: 30px;
    border: 1px solid #1A1A1A;
    width: 90%;
    max-width: 600px;
    max-height: 85vh;
    border-radius: 12px;
    position: relative;
    box-shadow: 0 15px 30px rgba(0, 191, 255, 0.3);
    animation: fadeIn 0.3s ease-out;
    display: flex;
    flex-direction: column;
    transform: translateY(-20px);
    transition: transform 0.3s ease;
    overflow-y: auto;
}

.modal-visible .modal-content {
    transform: translateY(0);
}



.modal-content h3 { color: #00BFFF; font-size: 1.875rem; font-weight: 700; margin-bottom: 1rem; }
.modal-content p { color: #E0E0E0; line-height: 1.7; margin-bottom: 0.75rem; }
.modal-content ul { list-style: disc; padding-left: 1.5rem; margin-bottom: 1rem; }
.modal-content ul li { margin-bottom: 0.5rem; color: #E0E0E0; }
.modal-content h4 { font-size: 1.5rem; margin-top: 1.5rem; margin-bottom: 0.75rem; color: #00BFFF; }

.modal-close {
    color: #E0E0E0; position: absolute; top: 15px; right: 25px;
    font-size: 28px; font-weight: bold; cursor: pointer; transition: color 0.2s ease;
}

.modal-close:hover, .modal-close:focus { color: #00BFFF; text-decoration: none; cursor: pointer; }

@keyframes fadeIn { from { opacity: 0; transform: translateY(-20px); } to { opacity: 1; transform: translateY(0); } }

/* Generic process step styling (used in modal content) */
.process-step { display: flex; align-items: flex-start; margin-bottom: 1rem; }
.process-icon { flex-shrink: 0; color: #00BFFF; font-size: 1.8rem; margin-right: 1rem; line-height: 1; }
.process-text h5 { font-weight: bold; font-size: 1.15rem; color: #E0E0E0; margin-bottom: 0.2rem; }
.process-text p { margin-bottom: 0; color: #A0A0A0; font-size: 0.95rem; }



/* Error Animation Styles */
@keyframes errorShake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

.form-error {
    animation: errorShake 0.5s ease-in-out;
    border-color: #ef4444 !important;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.3) !important;
}

.form-error-message {
    color: #ef4444;
    font-weight: 500;
    margin-top: 10px;
    opacity: 0;
    transform: translateY(-10px);
    transition: opacity 0.3s ease, transform 0.3s ease;
    text-align: center;
    padding: 8px 12px;
    background-color: rgba(239, 68, 68, 0.1);
    border-radius: 6px;
    border-left: 3px solid #ef4444;
}

.form-error-message.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Touch-friendly improvements */
.touch-manipulation {
    touch-action: manipulation;
    -webkit-tap-highlight-color: rgba(0, 191, 255, 0.3);
}

/* Mobile navigation improvements */
#mobile-menu {
    max-height: 0;
    overflow: hidden;
    opacity: 0;
    transform: translateY(-10px);
    transition: max-height 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                opacity 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    background: linear-gradient(135deg, rgba(17, 17, 17, 0.95) 0%, rgba(10, 10, 10, 0.98) 100%);
    border-radius: 0 0 12px 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

#mobile-menu.show {
    max-height: 500px;
    opacity: 1;
    transform: translateY(0);
}

/* Animate mobile menu items */
#mobile-menu .nav-link,
#mobile-menu .btn-primary {
    opacity: 0;
    transform: translateX(-20px);
    transition: opacity 0.3s ease-out, transform 0.3s ease-out;
}

#mobile-menu.show .nav-link,
#mobile-menu.show .btn-primary {
    opacity: 1;
    transform: translateX(0);
}

/* Stagger animation for menu items */
#mobile-menu.show .nav-link:nth-child(1) { transition-delay: 0.1s; }
#mobile-menu.show .nav-link:nth-child(2) { transition-delay: 0.15s; }
#mobile-menu.show .nav-link:nth-child(3) { transition-delay: 0.2s; }
#mobile-menu.show .nav-link:nth-child(4) { transition-delay: 0.25s; }
#mobile-menu.show .btn-primary { transition-delay: 0.3s; }

/* Mobile menu container improvements */
#mobile-menu .px-4 {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
}

/* Enhanced mobile menu button hover effect */
#mobile-menu-btn {
    transition: all 0.2s ease-in-out;
}

#mobile-menu-btn:hover {
    background-color: rgba(0, 191, 255, 0.1);
    border-radius: 8px;
}

#mobile-menu-btn:active {
    transform: scale(0.95);
}

/* Profile dropdown menu styles */
.dropdown-menu {
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: opacity 0.3s ease, visibility 0.3s ease, transform 0.3s ease;
    pointer-events: none;
}

.dropdown-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
    pointer-events: auto;
}

/* Better mobile form styling */
@media (max-width: 640px) {
    .modal-content {
        margin: 1rem;
        padding: 1.5rem;
        max-width: calc(100vw - 2rem);
        max-height: calc(100vh - 2rem);
    }



    /* Smaller cards for mobile */
    .integration-card {
        padding: 1rem;
    }

    .integration-card h4 {
        font-size: 0.875rem;
    }

    .integration-card p {
        font-size: 0.75rem;
    }

    .integration-card button {
        padding: 0.5rem 1rem;
        font-size: 0.75rem;
    }
}

/* Dashboard Content Area - Ensure proper spacing for all screen sizes */
#ai-dashboard-page .flex-1.p-8.overflow-y-auto {
    padding-top: 4rem; /* Add extra top padding to prevent nav overlap */
}

/* Desktop dashboard styles - hide mobile elements */
@media (min-width: 768px) {
    #mobile-menu-btn,
    #dashboard-mobile-menu-btn,
    #dashboard-mobile-overlay {
        display: none !important;
    }

    /* Hide swipe indicators on desktop */
    #ai-dashboard-page::before,
    #ai-dashboard-page::after {
        display: none !important;
    }

    #dashboard-sidebar {
        position: relative !important;
        transform: none !important;
        width: var(--dashboard-sidebar-width) !important;
        height: auto !important;
        z-index: auto !important;
        padding-top: 1.5rem !important;
    }
}

/* Media Queries for Dashboard internal layout */
@media (max-width: 767px) {

    /* Dashboard sidebar mobile styles */
    #dashboard-sidebar {
        position: fixed;
        top: 0;
        left: 0;
        height: 100vh;
        width: 256px;
        z-index: 50;
        transform: translateX(-100%);
        transition: transform 0.3s ease-in-out;
        padding-top: var(--nav-height);
        /* Ensure proper touch interaction */
        pointer-events: auto;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
    }

    #dashboard-sidebar.translate-x-0 {
        transform: translateX(0);
    }

    #dashboard-sidebar.-translate-x-full {
        transform: translateX(-100%);
    }

    /* Dashboard mobile overlay */
    #dashboard-mobile-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 40;
    }

    /* Dashboard content area mobile adjustments */
    #ai-dashboard-page .flex-1 {
        padding-left: 1rem;
        padding-right: 1rem;
        padding-top: 1rem;
        margin-left: 0;
    }

    /* Ensure dashboard navigation links are properly styled on mobile */
    #dashboard-sidebar-nav {
        flex-direction: column;
        gap: 0.75rem;
    }

    #dashboard-sidebar-nav .nav-link,
    #dashboard-sidebar-nav button {
        display: flex;
        align-items: center;
        padding: 0.75rem;
        border-radius: 0.375rem;
        transition: all 0.2s ease-in-out;
        text-decoration: none;
        font-size: 0.875rem;
        border: none;
        background: transparent;
        cursor: pointer;
        width: 100%;
        text-align: left;
        /* Mobile touch improvements */
        touch-action: manipulation;
        -webkit-tap-highlight-color: rgba(0, 191, 255, 0.3);
        user-select: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        /* Ensure minimum touch target size */
        min-height: 44px;
        position: relative;
        z-index: 10;
    }

    #dashboard-sidebar-nav .nav-link:hover,
    #dashboard-sidebar-nav button:hover {
        background-color: #1A1A1A;
    }

    /* Active state for mobile touch feedback */
    #dashboard-sidebar-nav .nav-link:active,
    #dashboard-sidebar-nav button:active {
        background-color: #2A2A2A;
        transform: scale(0.98);
    }

    #dashboard-sidebar-nav .nav-link:hover {
        color: #00BFFF;
    }

    /* Special styling for logout button */
    #dashboard-mobile-logout-btn {
        margin-top: 0.5rem;
        border-top: 1px solid #1A1A1A;
        padding-top: 1rem;
    }

    #dashboard-mobile-logout-btn:hover {
        color: #ff6b6b !important;
        background-color: rgba(255, 107, 107, 0.1) !important;
    }

    #dashboard-mobile-logout-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }

    /* Enhanced swipe indicator on the left edge - now primary navigation method */
    #ai-dashboard-page::before {
        content: '';
        position: fixed;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 6px;
        height: 80px;
        background: linear-gradient(to right, transparent, rgba(0, 191, 255, 0.4), rgba(0, 191, 255, 0.6));
        border-radius: 0 6px 6px 0;
        z-index: 30;
        opacity: 0.7;
        animation: swipeHint 4s ease-in-out infinite;
        box-shadow: 0 0 10px rgba(0, 191, 255, 0.3);
    }

    @keyframes swipeHint {
        0%, 100% {
            opacity: 0.4;
            transform: translateY(-50%) translateX(0);
        }
        50% {
            opacity: 0.9;
            transform: translateY(-50%) translateX(2px);
        }
    }

    /* Hide swipe indicator when menu is open */
    #ai-dashboard-page.menu-open::before {
        display: none;
    }

    /* Add a subtle text hint below the swipe indicator */
    #ai-dashboard-page::after {
        content: 'Swipe →';
        position: fixed;
        left: 12px;
        top: calc(50% + 50px);
        font-size: 0.75rem;
        color: rgba(0, 191, 255, 0.6);
        z-index: 30;
        opacity: 0.8;
        animation: textHint 4s ease-in-out infinite;
        pointer-events: none;
        font-weight: 500;
    }

    @keyframes textHint {
        0%, 100% { opacity: 0.5; }
        50% { opacity: 0.9; }
    }

    /* Hide text hint when menu is open */
    #ai-dashboard-page.menu-open::after {
        display: none;
    }

    #ai-dashboard-page .w-full.md\:w-64 h3 {
        text-align: center;
        margin-bottom: 1rem;
    }
    /* Dashboard Content Area */
    #ai-dashboard-page .flex-1.p-8.overflow-y-auto {
        padding: 1.5rem; /* Adjust padding for mobile */
        padding-top: 4rem; /* Extra top padding for mobile to prevent nav overlap */
        overflow-y: visible; /* Let parent section handle scroll if needed */
    }


}

/* Additional mobile optimizations */
@media (max-width: 480px) {
    /* Extra small screens */
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    /* Enhanced mobile scrolling for very small screens */
    body {
        overflow-x: hidden; /* Prevent horizontal scroll */
        overflow-y: auto; /* Ensure vertical scroll works */
    }

    /* FAQ section extra padding for very small screens */
    #faq.section-padding {
        padding-top: calc(var(--nav-height) + 2rem); /* Extra padding for small screens */
    }

    /* Smaller text on very small screens */
    h1 {
        font-size: 1.875rem !important; /* 30px */
        line-height: 1.2;
    }

    h2 {
        font-size: 1.5rem !important; /* 24px */
    }

    /* Adjust modal for very small screens */
    .modal-content {
        margin: 0.5rem;
        padding: 1rem;
        max-width: calc(100vw - 1rem);
        max-height: calc(100vh - 1rem);
    }





    /* Stack search and controls vertically on very small screens */
    #dashboard-company-management-view .flex.flex-col.gap-4 {
        gap: 1rem;
    }

    #dashboard-company-management-view .flex-1.max-w-md {
        max-width: 100%;
    }


}

/* FAQ Accordion Styles */
.faq-item {
    transition: all 0.3s ease;
}

.faq-item:hover {
    border-color: rgba(0, 191, 255, 0.3);
    box-shadow: 0 2px 8px rgba(0, 191, 255, 0.1);
}

.faq-question {
    transition: background-color 0.3s ease;
}

.faq-question:hover {
    background-color: #1A1A1A;
}

.faq-question i {
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.faq-answer {
    transition: max-height 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                opacity 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                padding 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    overflow: hidden;
    max-height: 0;
    opacity: 0;
    padding-top: 0;
    padding-bottom: 0;
    will-change: max-height, opacity, padding;
}

.faq-answer.expanding {
    opacity: 1;
    padding-top: 0.5rem;
    padding-bottom: 1rem;
}

.faq-answer p {
    line-height: 1.5;
    margin-bottom: 0;
    transform: translateZ(0); /* Force hardware acceleration */
}

/* FAQ Mobile Optimizations */
@media (max-width: 768px) {
    /* FAQ section specific padding override to prevent content cutoff */
    #faq.section-padding {
        padding-top: calc(var(--nav-height) + 2rem); /* Extra padding for mobile */
    }

    .faq-item {
        margin-bottom: 0.75rem;
    }

    .faq-question {
        padding: 1rem;
        font-size: 0.875rem;
    }

    .faq-answer {
        padding: 0 1rem 1rem 1rem;
        font-size: 0.875rem;
    }
}

/* Social Media Container - Pill Shape with Breathing Glow */
.social-media-container {
    display: flex;
    align-items: center;
    margin-top: 1.5rem;
    padding: 12px 20px;
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 50px; /* Pill/capsule shape */
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    width: fit-content;
}

.social-media-container:hover {
    transform: translateY(-1px);
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(0, 191, 255, 0.4);
    box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(0, 191, 255, 0.3);
    animation: subtleBorderPulse 3s ease-in-out infinite;
}

@keyframes subtleBorderPulse {
    0%, 100% {
        border-color: rgba(0, 191, 255, 0.3);
        box-shadow:
            0 2px 8px rgba(0, 0, 0, 0.1),
            0 0 0 1px rgba(0, 191, 255, 0.3);
    }
    50% {
        border-color: rgba(0, 191, 255, 0.5);
        box-shadow:
            0 2px 8px rgba(0, 0, 0, 0.1),
            0 0 0 1px rgba(0, 191, 255, 0.5);
    }
}

.social-links-group {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-left: 8px;
}

/* Individual Social Media Links Styling */
.social-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.social-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 191, 255, 0.15), rgba(0, 255, 255, 0.15));
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 50%;
}

.social-link:hover {
    transform: translateY(-1px) scale(1.05);
    background: rgba(0, 191, 255, 0.12);
    border-color: rgba(0, 191, 255, 0.4);
    box-shadow:
        0 4px 20px rgba(0, 191, 255, 0.25),
        0 0 0 2px rgba(0, 191, 255, 0.1);
}

.social-link:hover::before {
    opacity: 1;
}

.social-link:hover i {
    transform: scale(1.15);
    color: #00BFFF;
}

.social-link i {
    transition: all 0.3s ease;
    z-index: 1;
    position: relative;
}

/* Responsive adjustments for social container and links */
@media (max-width: 640px) {
    .social-media-container {
        padding: 10px 16px;
        margin-top: 1rem;
    }

    .social-link {
        width: 36px;
        height: 36px;
    }

    .social-link i {
        font-size: 1rem;
    }

    .social-links-group {
        gap: 6px;
        margin-left: 6px;
    }
}

/* Landscape orientation optimizations for mobile */
@media (max-height: 500px) and (orientation: landscape) {
    .section-padding {
        padding-top: calc(var(--nav-height) + 0.5rem); /* Minimal padding for landscape */
        padding-bottom: 1rem;
        min-height: auto;
    }

    /* Reduce vertical spacing in landscape */
    h1, h2 {
        margin-bottom: 1rem;
    }


}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    /* Ensure crisp borders and shadows on high DPI displays */
    .btn-primary, .modal-content {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
}

/* Chart.js Specific Styling (Optional, for better appearance) */
.chart-container {
    position: relative;
    height: 400px; /* Adjust height as needed */
    width: 100%;
    max-width: 800px; /* Limit max width */
    margin: 2rem auto; /* Center the chart */
}

@media (max-width: 768px) {
    .chart-container {
        height: 300px;
        margin: 1rem auto;
    }
}

/* Ensure chart tooltips are visible */
.chartjs-tooltip {
     background: #0A0A0A !important;
     border: 1px solid #1A1A1A !important;
     color: #E0E0E0 !important;
     padding: 8px !important;
     border-radius: 4px !important;
}

.chartjs-tooltip-key {
      display: inline-block;
      width: 10px;
      height: 10px;
      margin-right: 8px;
      border-radius: 50%;
}

/* Accessibility improvements for mobile */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Constellation Animation Styles */
#large-header {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    pointer-events: none; /* Allow clicks to pass through to content below */
}

#demo-canvas {
    background: transparent;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    pointer-events: none; /* Allow clicks to pass through to content below */
}

/* Ensure home section content stays above the canvas */
#home .container {
    position: relative;
    z-index: 10;
}

/* Focus improvements for keyboard navigation */
/* Keep focus outlines for form elements and buttons (accessibility) */
input:focus, textarea:focus, button:not(.nav-link):focus {
    outline: 2px solid #00BFFF;
    outline-offset: 2px;
}

/* Remove focus outlines for navigation links */
a.nav-link:focus, a[href^="#"]:focus {
    outline: none;
}

/* Better focus visibility on mobile */
@media (max-width: 768px) {
    input:focus, textarea:focus, button:not(.nav-link):focus {
        outline-width: 3px;
    }
}
